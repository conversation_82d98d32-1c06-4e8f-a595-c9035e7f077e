import React, { useState, memo } from 'react'
import { useAuthContext } from './AuthProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Eye, EyeOff } from 'lucide-react'
import { Link } from 'react-router-dom'
import { LoadingStateManager, useLoadingState } from '@/components/common/LoadingStateManager'
import { FormErrorBoundary } from '@/utils/errorBoundaryHelpers'
import { useOptimizedCallback } from '@/hooks/useOptimization'
import { useTouchTargets, useScreenReader } from '@/hooks/useAccessibility'
import { FormSkeleton } from '@/components/ui/skeleton-variants'

interface LoginFormProps {
  onSuccess?: () => void
  redirectTo?: string
}

// Memoized LoginForm component for better performance
const LoginFormComponent = memo(function LoginForm({ onSuccess, redirectTo }: LoginFormProps) {
  const { signIn, loading } = useAuthContext()
  const { isLoading, error: loadingError, startLoading, stopLoading, setLoadingError } = useLoadingState()
  const { ensureTouchTarget } = useTouchTargets()
  const { announce } = useScreenReader()

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Optimized submit handler with proper loading states and accessibility
  const handleSubmit = useOptimizedCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    startLoading()
    setIsSubmitting(true)

    // Announce loading state to screen readers
    announce('جاري تسجيل الدخول...', 'polite')

    try {
      const { error } = await signIn(formData.email, formData.password)

      if (error) {
        let errorMessage = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى'

        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = 'يرجى تأكيد البريد الإلكتروني أولاً'
        }

        setError(errorMessage)
        setLoadingError(errorMessage)
        announce(`خطأ: ${errorMessage}`, 'assertive')
      } else {
        announce('تم تسجيل الدخول بنجاح', 'polite')
        stopLoading()
        onSuccess?.()
        if (redirectTo) {
          window.location.href = redirectTo
        }
      }
    } catch (err) {
      const errorMessage = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى'
      setError(errorMessage)
      setLoadingError(errorMessage)
      announce(`خطأ: ${errorMessage}`, 'assertive')
    } finally {
      setIsSubmitting(false)
      if (isLoading) stopLoading()
    }
  }, [signIn, formData.email, formData.password, onSuccess, redirectTo, startLoading, stopLoading, setLoadingError, announce, isLoading])

  // Optimized change handler
  const handleChange = useOptimizedCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }, [error])

  return (
    <LoadingStateManager
      isLoading={isLoading || loading}
      loadingComponent={
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">تسجيل الدخول</CardTitle>
            <CardDescription>
              أدخل بياناتك للوصول إلى منصة الحلول التقنية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FormSkeleton
              fields={2}
              showSubmitButton={true}
              submitButtonText="جاري تسجيل الدخول..."
            />
          </CardContent>
        </Card>
      }
      fadeTransition={true}
      transitionDuration={300}
    >
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">تسجيل الدخول</CardTitle>
          <CardDescription>
            أدخل بياناتك للوصول إلى منصة الحلول التقنية
          </CardDescription>
        </CardHeader>
        <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4" noValidate>
          {(error || loadingError) && (
            <Alert variant="destructive" id="login-error" role="alert">
              <AlertDescription>{error || loadingError}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="email">البريد الإلكتروني</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
              disabled={isSubmitting || isLoading}
              dir="ltr"
              className="min-h-[44px] touch-manipulation"
              aria-describedby={error ? "login-error" : undefined}
              aria-invalid={error ? "true" : "false"}
              ref={(el) => el && ensureTouchTarget(el)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">كلمة المرور</Label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleChange}
                placeholder="أدخل كلمة المرور"
                required
                disabled={isSubmitting || isLoading}
                dir="ltr"
                className="min-h-[44px] touch-manipulation pr-12"
                aria-describedby={error ? "login-error" : undefined}
                aria-invalid={error ? "true" : "false"}
                ref={(el) => el && ensureTouchTarget(el)}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent min-w-[44px] min-h-[44px] touch-manipulation"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isSubmitting || isLoading}
                aria-label={showPassword ? "إخفاء كلمة المرور" : "إظهار كلمة المرور"}
                ref={(el) => el && ensureTouchTarget(el)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          <Button
            type="submit"
            className="w-full min-h-[44px] touch-manipulation"
            disabled={isSubmitting || loading || isLoading}
            aria-describedby={error ? "login-error" : undefined}
            ref={(el) => el && ensureTouchTarget(el)}
          >
            {(isSubmitting || isLoading) ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                جاري تسجيل الدخول...
              </>
            ) : (
              'تسجيل الدخول'
            )}
          </Button>

          <div className="text-center space-y-2">
            <Link 
              to="/auth/forgot-password" 
              className="text-sm text-blue-600 hover:underline"
            >
              نسيت كلمة المرور؟
            </Link>
            <div className="text-sm text-gray-600">
              ليس لديك حساب؟{' '}
              <Link 
                to="/auth/register" 
                className="text-blue-600 hover:underline"
              >
                إنشاء حساب جديد
              </Link>
            </div>
          </div>
        </form>
        </CardContent>
      </Card>
    </LoadingStateManager>
  )
})

// Export wrapped component with error boundary
export function LoginForm(props: LoginFormProps) {
  return (
    <FormErrorBoundary formName="Login">
      <LoginFormComponent {...props} />
    </FormErrorBoundary>
  )
}