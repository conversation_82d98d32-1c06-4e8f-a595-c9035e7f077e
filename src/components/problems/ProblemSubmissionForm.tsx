import React, { useState, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { useDeviceType } from '@/hooks/use-mobile';
import { problemOperations } from '@/lib/database';
import { FileUpload, UploadedFile } from './FileUpload';
import { Loader2, Tag, X } from 'lucide-react';
import { LoadingStateManager, useLoadingState } from '@/components/common/LoadingStateManager';
import { FormErrorBoundary } from '@/utils/errorBoundaryHelpers';
import { useOptimizedCallback } from '@/hooks/useOptimization';
import { useTouchTargets, useScreenReader } from '@/hooks/useAccessibility';
import { FormSkeleton } from '@/components/ui/skeleton-variants';

interface ProblemFormData {
  title: string;
  description: string;
  category: string;
  sector: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  tags: string[];
  attachments: UploadedFile[];
}

const SECTORS = [
  // الوزارات الحكومية
  'وزارة الصحة',
  'وزارة التربية والتعليم العالي',
  'وزارة المالية',
  'وزارة الاتصالات والتقانة',
  'وزارة الصناعة والمعادن',
  'وزارة الزراعة والإصلاح الزراعي',
  'وزارة النقل',
  'وزارة الداخلية',
  'وزارة العدل',
  'وزارة الخارجية والمغتربين',
  'وزارة الدفاع',
  'وزارة الثقافة',
  'وزارة السياحة',
  'وزارة الشؤون الاجتماعية والعمل',
  'وزارة الإسكان والتعمير',
  'وزارة البيئة',
  'وزارة الطاقة',
  'وزارة المياه',
  'وزارة التجارة الداخلية وحماية المستهلك',
  'وزارة الاقتصاد والتجارة الخارجية',
  'وزارة الأوقاف',
  'وزارة الإعلام',
  'وزارة التعليم العالي والبحث العلمي',
  'وزارة الشباب والرياضة',
  
  // المؤسسات الحكومية
  'رئاسة مجلس الوزراء',
  'مجلس الشعب',
  'المحكمة الدستورية العليا',
  'ديوان المحاسبة',
  'الهيئة العامة للرقابة والتفتيش',
  'المصرف المركزي السوري',
  'هيئة الأوراق والأسواق المالية',
  'الهيئة العامة للاستثمار',
  'مؤسسة الإذاعة والتلفزيون',
  'الهيئة العامة للطيران المدني',
  'الهيئة العامة للاتصالات والبريد',
  'الهيئة العامة للتقييس',
  'الهيئة العامة للمواصفات والمقاييس',
  
  // القطاعات الاقتصادية
  'القطاع المصرفي',
  'قطاع التأمين',
  'قطاع الطاقة والنفط',
  'قطاع الصناعات الغذائية',
  'قطاع الصناعات النسيجية',
  'قطاع الصناعات الكيماوية',
  'قطاع الصناعات المعدنية',
  'قطاع البناء والإنشاءات',
  'قطاع السياحة والفندقة',
  'قطاع النقل واللوجستيات',
  'قطاع التجارة والتوزيع',
  'قطاع الزراعة والثروة الحيوانية',
  
  // القطاع التقني والتعليمي
  'الجامعات الحكومية',
  'الجامعات الخاصة',
  'المعاهد التقنية',
  'مراكز البحث العلمي',
  'الشركات التقنية',
  'الشركات الناشئة',
  'حاضنات الأعمال',
  'مراكز الابتكار',
  
  // القطاع الخاص
  'الشركات الكبرى',
  'الشركات المتوسطة',
  'الشركات الصغيرة',
  'المؤسسات الفردية',
  'الجمعيات والمنظمات',
  'المنظمات غير الحكومية',
  
  'أخرى'
];

const CATEGORIES = [
  // تطوير البرمجيات
  'تطوير تطبيقات الويب',
  'تطوير تطبيقات الهاتف المحمول',
  'تطوير أنظمة سطح المكتب',
  'تطوير واجهات برمجة التطبيقات (APIs)',
  'هندسة البرمجيات',
  'إدارة المشاريع التقنية',
  
  // قواعد البيانات وإدارة البيانات
  'تصميم قواعد البيانات',
  'إدارة قواعد البيانات',
  'تحليل البيانات الضخمة',
  'ذكاء الأعمال (BI)',
  'مستودعات البيانات',
  'تكامل البيانات',
  
  // أمن المعلومات والحماية
  'أمن الشبكات',
  'أمن التطبيقات',
  'الحماية من الفيروسات',
  'إدارة الهوية والوصول',
  'التشفير وحماية البيانات',
  'اختبار الاختراق',
  'الامتثال والحوكمة',
  
  // الشبكات والبنية التحتية
  'تصميم الشبكات',
  'إدارة الخوادم',
  'الحوسبة السحابية',
  'المحاكاة الافتراضية',
  'النسخ الاحتياطي والاستعادة',
  'مراقبة الأداء',
  
  // الذكاء الاصطناعي والتقنيات الحديثة
  'الذكاء الاصطناعي',
  'تعلم الآلة',
  'معالجة اللغات الطبيعية',
  'الرؤية الحاسوبية',
  'إنترنت الأشياء (IoT)',
  'البلوك تشين',
  'الواقع المعزز والافتراضي',
  
  // الأنظمة المؤسسية
  'أنظمة إدارة المحتوى (CMS)',
  'أنظمة إدارة الموارد البشرية',
  'أنظمة إدارة العلاقات مع العملاء (CRM)',
  'أنظمة تخطيط موارد المؤسسة (ERP)',
  'أنظمة إدارة الوثائق',
  'أنظمة المحاسبة والمالية',
  
  // التجارة الإلكترونية والدفع
  'منصات التجارة الإلكترونية',
  'أنظمة الدفع الإلكتروني',
  'إدارة المخزون الرقمي',
  'التسويق الرقمي',
  'تحليل سلوك المستخدمين',
  
  // الاتصالات والوسائط
  'أنظمة الاتصالات الموحدة',
  'معالجة الصوت والفيديو',
  'البث المباشر',
  'إدارة المحتوى الرقمي',
  'أنظمة المؤتمرات المرئية',
  
  // التعليم والتدريب الإلكتروني
  'منصات التعلم الإلكتروني',
  'أنظمة إدارة التعلم (LMS)',
  'المحتوى التفاعلي',
  'التقييم الإلكتروني',
  'التدريب الافتراضي',
  
  // الصحة الرقمية
  'أنظمة المعلومات الصحية',
  'السجلات الطبية الإلكترونية',
  'التطبيب عن بُعد',
  'أجهزة المراقبة الطبية',
  'تحليل البيانات الطبية',
  
  // الحكومة الإلكترونية
  'الخدمات الحكومية الرقمية',
  'أنظمة إدارة الهوية الرقمية',
  'منصات المشاركة المواطنية',
  'أنظمة الأرشفة الإلكترونية',
  'التوقيع الإلكتروني',
  
  // مشاكل عامة
  'مشاكل الأداء والسرعة',
  'مشاكل التوافق والتكامل',
  'مشاكل واجهة المستخدم',
  'مشاكل الصيانة والدعم',
  'مشاكل الترقية والتحديث',
  'مشاكل التدريب والتأهيل',
  
  'أخرى'
];

const URGENCY_LEVELS = [
  { value: 'low', label: 'منخفضة', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: 'متوسطة', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'عالية', color: 'bg-orange-100 text-orange-800' },
  { value: 'critical', label: 'حرجة', color: 'bg-red-100 text-red-800' }
];

// Memoized ProblemSubmissionForm component for better performance
const ProblemSubmissionFormComponent = memo(function ProblemSubmissionForm() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuthContext();
  const { isMobile } = useDeviceType();
  const { isLoading, error: loadingError, startLoading, stopLoading, setLoadingError } = useLoadingState();
  const { ensureTouchTarget } = useTouchTargets();
  const { announce } = useScreenReader();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newTag, setNewTag] = useState('');

  const [formData, setFormData] = useState<ProblemFormData>({
    title: '',
    description: '',
    category: '',
    sector: '',
    urgency: 'medium',
    tags: [],
    attachments: []
  });

  const [errors, setErrors] = useState<Partial<ProblemFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<ProblemFormData> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'العنوان مطلوب';
    } else if (formData.title.length < 10) {
      newErrors.title = 'العنوان يجب أن يكون 10 أحرف على الأقل';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'الوصف مطلوب';
    } else if (formData.description.length < 50) {
      newErrors.description = 'الوصف يجب أن يكون 50 حرف على الأقل';
    }

    if (!formData.category) {
      newErrors.category = 'الفئة التقنية مطلوبة';
    }

    if (!formData.sector) {
      newErrors.sector = 'القطاع مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Optimized submit handler with proper loading states and accessibility
  const handleSubmit = useOptimizedCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "خطأ في المصادقة",
        description: "يجب تسجيل الدخول أولاً",
        variant: "destructive",
      });
      return;
    }

    if (!validateForm()) {
      const errorMessage = "يرجى تصحيح الأخطاء المذكورة"
      toast({
        title: "خطأ في البيانات",
        description: errorMessage,
        variant: "destructive",
      });
      announce(`خطأ: ${errorMessage}`, 'assertive')
      return;
    }

    startLoading();
    setIsSubmitting(true);
    announce('جاري إرسال المشكلة...', 'polite');

    try {
      // Prepare attachments data
      const attachments = formData.attachments
        .filter(file => file.uploadStatus === 'success')
        .map(file => ({
          id: file.id,
          name: file.name,
          size: file.size,
          type: file.type,
          url: file.url
        }));

      const { data, error } = await problemOperations.createProblem({
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category,
        sector: formData.sector,
        urgency: formData.urgency,
        submitted_by: user.id,
        tags: formData.tags,
        attachments,
        status: 'open'
      });

      if (error) {
        throw error;
      }

      const successMessage = "تم إرسال المشكلة بنجاح! سيتم مراجعة المشكلة وإشعار الخبراء المناسبين"
      toast({
        title: "تم إرسال المشكلة بنجاح",
        description: successMessage,
      });

      announce(successMessage, 'polite');
      stopLoading();

      // Navigate to the problem details page
      navigate(`/problems/${data.id}`);
    } catch (error) {
      console.error('Error creating problem:', error);
      const errorMessage = "حدث خطأ أثناء إرسال المشكلة. يرجى المحاولة مرة أخرى"
      toast({
        title: "خطأ في الإرسال",
        description: errorMessage,
        variant: "destructive",
      });
      setLoadingError(errorMessage);
      announce(`خطأ: ${errorMessage}`, 'assertive');
    } finally {
      setIsSubmitting(false);
      if (isLoading) stopLoading();
    }
  }, [user, validateForm, formData, toast, navigate, startLoading, stopLoading, setLoadingError, announce, isLoading]);

  const addTag = () => {
    const tag = newTag.trim();
    if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <LoadingStateManager
      isLoading={isLoading}
      loadingComponent={
        <div className={`mx-auto ${isMobile ? 'p-4' : 'max-w-4xl p-6'}`} dir="rtl">
          <Card>
            <CardHeader className={isMobile ? 'p-4' : ''}>
              <CardTitle className={`${isMobile ? 'text-xl' : 'text-2xl'}`}>إرسال مشكلة تقنية جديدة</CardTitle>
              <CardDescription className={isMobile ? 'text-sm' : ''}>
                املأ النموذج التالي لإرسال مشكلة تقنية جديدة
              </CardDescription>
            </CardHeader>
            <CardContent className={isMobile ? 'p-4' : ''}>
              <FormSkeleton
                fields={6}
                showSubmitButton={true}
                submitButtonText="جاري إرسال المشكلة..."
              />
            </CardContent>
          </Card>
        </div>
      }
      fadeTransition={true}
      transitionDuration={300}
    >
      <div className={`mx-auto ${isMobile ? 'p-4' : 'max-w-4xl p-6'}`} dir="rtl">
        <Card>
          <CardHeader className={isMobile ? 'p-4' : ''}>
            <CardTitle className={`${isMobile ? 'text-xl' : 'text-2xl'}`}>إرسال مشكلة تقنية جديدة</CardTitle>
            <CardDescription className={isMobile ? 'text-sm' : ''}>
            اشرح المشكلة التقنية التي تواجهها بالتفصيل ليتمكن الخبراء من تقديم أفضل الحلول
          </CardDescription>
        </CardHeader>
        <CardContent className={isMobile ? 'p-4' : ''}>
          <form onSubmit={handleSubmit} className={`${isMobile ? 'space-y-4' : 'space-y-6'}`}>
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">عنوان المشكلة *</Label>
              <Input
                id="title"
                placeholder="مثال: مشكلة في تكامل نظام إدارة المرضى مع قاعدة البيانات"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-sm text-red-600">{errors.title}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">وصف تفصيلي للمشكلة *</Label>
              <Textarea
                id="description"
                placeholder="اشرح المشكلة بالتفصيل: ما هي المشكلة؟ متى تحدث؟ ما هي الأعراض؟ ما هي البيئة التقنية المستخدمة؟ ما هي المحاولات السابقة لحل المشكلة؟"
                rows={8}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description}</p>
              )}
              <p className="text-sm text-gray-500">
                {formData.description.length} حرف (الحد الأدنى: 50 حرف)
              </p>
            </div>

            {/* Category and Sector */}
            <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'md:grid-cols-2 gap-4'}`}>
              <div className="space-y-2">
                <Label htmlFor="category">الفئة التقنية *</Label>
                <Select 
                  value={formData.category} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                    <SelectValue placeholder="اختر الفئة التقنية" />
                  </SelectTrigger>
                  <SelectContent>
                    {CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-red-600">{errors.category}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="sector">القطاع *</Label>
                <Select 
                  value={formData.sector} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, sector: value }))}
                >
                  <SelectTrigger className={errors.sector ? 'border-red-500' : ''}>
                    <SelectValue placeholder="اختر القطاع" />
                  </SelectTrigger>
                  <SelectContent>
                    {SECTORS.map(sector => (
                      <SelectItem key={sector} value={sector}>
                        {sector}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.sector && (
                  <p className="text-sm text-red-600">{errors.sector}</p>
                )}
              </div>
            </div>

            {/* Urgency */}
            <div className="space-y-2">
              <Label htmlFor="urgency">مستوى الأولوية</Label>
              <Select 
                value={formData.urgency} 
                onValueChange={(value: 'low' | 'medium' | 'high' | 'critical') => 
                  setFormData(prev => ({ ...prev, urgency: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {URGENCY_LEVELS.map(level => (
                    <SelectItem key={level.value} value={level.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${level.color}`} />
                        {level.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="tags">الكلمات المفتاحية (اختياري)</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="أضف كلمة مفتاحية"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                />
                <Button 
                  type="button" 
                  onClick={addTag} 
                  variant="outline"
                  disabled={!newTag.trim() || formData.tags.length >= 10}
                >
                  <Tag className="w-4 h-4 ml-2" />
                  إضافة
                </Button>
              </div>
              
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X 
                        className="w-3 h-3 cursor-pointer hover:text-red-600" 
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
              <p className="text-sm text-gray-500">
                {formData.tags.length}/10 كلمات مفتاحية
              </p>
            </div>

            {/* File Upload */}
            <div className="space-y-2">
              <Label>المرفقات (اختياري)</Label>
              <FileUpload
                onFilesChange={(files) => setFormData(prev => ({ ...prev, attachments: files }))}
                disabled={isSubmitting}
              />
            </div>

            {/* Submit Button */}
            <div className={`${isMobile ? 'flex flex-col space-y-3 pt-4' : 'flex justify-between items-center pt-6'}`}>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => navigate(-1)}
                disabled={isSubmitting}
                className={`${isMobile ? 'w-full order-2' : ''} touch-manipulation`}
              >
                إلغاء
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className={`bg-blue-600 hover:bg-blue-700 touch-manipulation ${isMobile ? 'w-full order-1' : ''}`}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                    {isMobile ? 'إرسال...' : 'جاري الإرسال...'}
                  </>
                ) : (
                  'إرسال المشكلة'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
        </Card>
      </div>
    </LoadingStateManager>
  );
})

// Export wrapped component with error boundary
export function ProblemSubmissionForm() {
  return (
    <FormErrorBoundary formName="ProblemSubmission">
      <ProblemSubmissionFormComponent />
    </FormErrorBoundary>
  );
}