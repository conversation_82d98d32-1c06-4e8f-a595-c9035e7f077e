import { useState, useEffect, useRef, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { useToast } from '@/hooks/use-toast'
import { useDeviceType, useTouchSupport } from '@/hooks/use-mobile'
import { SearchFilters } from './SearchFilters'
import { searchService } from '@/lib/search'
import { 
  SearchFilters as SearchFiltersType, 
  SearchSuggestion, 
  SortOption,
  filtersToUrlParams,
  urlParamsToFilters,
  validateFilters
} from '@/lib/search'
import { useSearchAnalytics } from '@/lib/search/SearchAnalytics'
import { useOptimizedCallback, useMemoizedValue, useDebouncedValue } from '@/hooks/useOptimization'
import { useOptimizedSearch } from '@/hooks/useFormOptimization'
import { 
  Search, 
  X,
  Loader2,
  Settings,
  ChevronDown,
  ChevronUp,
  Mic,
  MicOff,
  Globe,
  History,
  TrendingUp,
  Tag,
  User,
  FileText,
  ArrowRight,
  Sparkles,
  Menu,
  Filter
} from 'lucide-react'

interface EnhancedSearchInterfaceProps {
  onSearch?: (query: string, filters: SearchFiltersType, sortBy: SortOption) => void
  initialQuery?: string
  initialFilters?: SearchFiltersType
  initialSortBy?: SortOption
  showFilters?: boolean
  placeholder?: string
  className?: string
}

interface SearchMode {
  id: 'simple' | 'advanced'
  label: string
  description: string
}

const SEARCH_MODES: SearchMode[] = [
  {
    id: 'simple',
    label: 'بحث بسيط',
    description: 'بحث سريع في جميع المحتويات'
  },
  {
    id: 'advanced',
    label: 'بحث متقدم',
    description: 'بحث مفصل مع فلاتر متقدمة'
  }
]

export function EnhancedSearchInterface({
  onSearch,
  initialQuery = '',
  initialFilters = {},
  initialSortBy = 'relevance',
  showFilters = true,
  placeholder = 'ابحث في المشاكل، الخبراء، والحلول...',
  className = ''
}: EnhancedSearchInterfaceProps) {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const { toast } = useToast()
  const { trackSuggestion, trackFilter } = useSearchAnalytics()
  const { isMobile, isTablet, isTouchDevice } = useDeviceType()
  const hasTouch = useTouchSupport()
  
  // Search state
  const [query, setQuery] = useState(initialQuery)
  const [filters, setFilters] = useState<SearchFiltersType>(initialFilters)
  const [sortBy, setSortBy] = useState<SortOption>(initialSortBy)
  const [searchMode, setSearchMode] = useState<'simple' | 'advanced'>('simple')
  
  // UI state
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const [isVoiceRecording, setIsVoiceRecording] = useState(false)
  const [detectedLanguage, setDetectedLanguage] = useState<'ar' | 'en' | 'auto'>('auto')
  const [isMobileFiltersOpen, setIsMobileFiltersOpen] = useState(false)
  const [isProgressiveLoading, setIsProgressiveLoading] = useState(false)
  
  // Refs
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const debounceRef = useRef<NodeJS.Timeout>()
  const recognitionRef = useRef<SpeechRecognition | null>(null)
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null)
  
  // Initialize from URL params
  useEffect(() => {
    const urlState = urlParamsToFilters(searchParams)
    if (urlState.query !== query) {
      setQuery(urlState.query)
    }
    if (JSON.stringify(urlState.filters) !== JSON.stringify(filters)) {
      setFilters(urlState.filters)
    }
    if (urlState.sortBy !== sortBy) {
      setSortBy(urlState.sortBy)
    }
  }, [searchParams])
  
  // Auto-detect language
  useEffect(() => {
    if (query.trim()) {
      const arabicRegex = /[\u0600-\u06FF]/
      const englishRegex = /[a-zA-Z]/
      
      const hasArabic = arabicRegex.test(query)
      const hasEnglish = englishRegex.test(query)
      
      if (hasArabic && !hasEnglish) {
        setDetectedLanguage('ar')
      } else if (hasEnglish && !hasArabic) {
        setDetectedLanguage('en')
      } else {
        setDetectedLanguage('auto')
      }
    }
  }, [query])
  
  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('touchstart', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('touchstart', handleClickOutside)
    }
  }, [])

  // Touch gesture handling for mobile filter navigation
  useEffect(() => {
    if (!isTouchDevice) return

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        touchStartRef.current = {
          x: e.touches[0].clientX,
          y: e.touches[0].clientY,
          time: Date.now()
        }
      }
    }

    const handleTouchEnd = (e: TouchEvent) => {
      if (!touchStartRef.current || e.changedTouches.length !== 1) return

      const touchEnd = {
        x: e.changedTouches[0].clientX,
        y: e.changedTouches[0].clientY,
        time: Date.now()
      }

      const deltaX = touchEnd.x - touchStartRef.current.x
      const deltaY = touchEnd.y - touchStartRef.current.y
      const deltaTime = touchEnd.time - touchStartRef.current.time

      // Detect swipe gestures (minimum 50px distance, maximum 300ms duration)
      if (Math.abs(deltaX) > 50 && Math.abs(deltaY) < 100 && deltaTime < 300) {
        // Swipe right to open filters on mobile
        if (deltaX > 0 && isMobile && showFilters) {
          setIsMobileFiltersOpen(true)
        }
        // Swipe left to close filters on mobile
        else if (deltaX < 0 && isMobile && isMobileFiltersOpen) {
          setIsMobileFiltersOpen(false)
        }
      }

      touchStartRef.current = null
    }

    document.addEventListener('touchstart', handleTouchStart, { passive: true })
    document.addEventListener('touchend', handleTouchEnd, { passive: true })

    return () => {
      document.removeEventListener('touchstart', handleTouchStart)
      document.removeEventListener('touchend', handleTouchEnd)
    }
  }, [isTouchDevice, isMobile, showFilters, isMobileFiltersOpen])

  // Progressive loading for slow connections
  useEffect(() => {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    
    if (connection) {
      const isSlowConnection = connection.effectiveType === 'slow-2g' || 
                              connection.effectiveType === '2g' || 
                              connection.downlink < 1.5

      setIsProgressiveLoading(isSlowConnection)
    }
  }, [])
  
  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      recognitionRef.current = new SpeechRecognition()
      
      if (recognitionRef.current) {
        recognitionRef.current.continuous = false
        recognitionRef.current.interimResults = false
        recognitionRef.current.lang = detectedLanguage === 'ar' ? 'ar-SA' : 'en-US'
        
        recognitionRef.current.onresult = (event) => {
          const transcript = event.results[0][0].transcript
          setQuery(transcript)
          setIsVoiceRecording(false)
          
          // Auto-search after voice input
          setTimeout(() => {
            handleSearch(transcript)
          }, 500)
        }
        
        recognitionRef.current.onerror = (event) => {
          console.error('Speech recognition error:', event.error)
          setIsVoiceRecording(false)
          toast({
            title: 'خطأ في التعرف على الصوت',
            description: 'لم نتمكن من التعرف على الصوت. يرجى المحاولة مرة أخرى.',
            variant: 'destructive'
          })
        }
        
        recognitionRef.current.onend = () => {
          setIsVoiceRecording(false)
        }
      }
    }
  }, [detectedLanguage])
  
  // Debounced suggestions loading
  const loadSuggestions = useCallback(async (searchQuery: string) => {
    if (searchQuery.trim().length < 2) {
      setSuggestions([])
      return
    }
    
    setIsLoadingSuggestions(true)
    try {
      const suggestions = await searchService.suggest(searchQuery.trim(), 8)
      setSuggestions(suggestions)
    } catch (error) {
      console.error('Error loading suggestions:', error)
      setSuggestions([])
    } finally {
      setIsLoadingSuggestions(false)
    }
  }, [])
  
  // Handle query changes with debouncing
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }
    
    debounceRef.current = setTimeout(() => {
      if (query.trim() && showSuggestions) {
        loadSuggestions(query.trim())
      } else {
        setSuggestions([])
      }
    }, 300) // 300ms debounce as specified in requirements
    
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [query, showSuggestions, loadSuggestions])
  
  const handleSearch = useCallback((searchQuery?: string) => {
    const finalQuery = searchQuery || query
    if (!finalQuery.trim()) return
    
    const validatedFilters = validateFilters(filters)
    
    // Update URL with search parameters
    const params = filtersToUrlParams(validatedFilters, finalQuery.trim(), sortBy)
    setSearchParams(params)
    
    // Close suggestions
    setShowSuggestions(false)
    
    // Call external search handler or navigate
    if (onSearch) {
      onSearch(finalQuery.trim(), validatedFilters, sortBy)
    } else {
      navigate(`/search?${params.toString()}`)
    }
    
    // Save to recent searches
    saveRecentSearch(finalQuery.trim())
  }, [query, filters, sortBy, onSearch, navigate, setSearchParams])
  
  const handleSuggestionClick = async (suggestion: SearchSuggestion) => {
    // Track suggestion click
    try {
      await trackSuggestion(suggestion.text, suggestion.type)
    } catch (error) {
      console.warn('Failed to track suggestion click:', error)
    }
    
    setQuery(suggestion.text)
    handleSearch(suggestion.text)
  }
  
  const handleFiltersChange = async (newFilters: SearchFiltersType) => {
    // Track filter changes
    const changedFilters = Object.keys(newFilters).filter(key => {
      const oldValue = filters[key as keyof SearchFiltersType]
      const newValue = newFilters[key as keyof SearchFiltersType]
      return JSON.stringify(oldValue) !== JSON.stringify(newValue)
    })
    
    for (const filterKey of changedFilters) {
      try {
        await trackFilter(filterKey, newFilters[filterKey as keyof SearchFiltersType])
      } catch (error) {
        console.warn('Failed to track filter change:', error)
      }
    }
    
    setFilters(newFilters)
  }
  
  const handleVoiceSearch = () => {
    if (!recognitionRef.current) {
      toast({
        title: 'البحث الصوتي غير مدعوم',
        description: 'متصفحك لا يدعم البحث الصوتي.',
        variant: 'destructive'
      })
      return
    }
    
    if (isVoiceRecording) {
      recognitionRef.current.stop()
      setIsVoiceRecording(false)
    } else {
      setIsVoiceRecording(true)
      recognitionRef.current.start()
    }
  }
  
  const saveRecentSearch = (searchQuery: string) => {
    try {
      const recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]')
      const updatedSearches = [
        searchQuery,
        ...recentSearches.filter((s: string) => s !== searchQuery)
      ].slice(0, 10) // Keep only 10 recent searches
      
      localStorage.setItem('recentSearches', JSON.stringify(updatedSearches))
    } catch (error) {
      console.error('Error saving recent search:', error)
    }
  }
  
  const getRecentSearches = (): string[] => {
    try {
      return JSON.parse(localStorage.getItem('recentSearches') || '[]')
    } catch (error) {
      console.error('Error loading recent searches:', error)
      return []
    }
  }
  
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'recent': return <History className="w-4 h-4 text-gray-400" />
      case 'popular': return <TrendingUp className="w-4 h-4 text-orange-400" />
      case 'category': return <FileText className="w-4 h-4 text-blue-400" />
      case 'expert': return <User className="w-4 h-4 text-green-400" />
      case 'tag': return <Tag className="w-4 h-4 text-purple-400" />
      default: return <Search className="w-4 h-4 text-gray-400" />
    }
  }
  
  const getSuggestionTypeLabel = (type: string) => {
    switch (type) {
      case 'recent': return 'بحث سابق'
      case 'popular': return 'شائع'
      case 'category': return 'فئة'
      case 'expert': return 'خبير'
      case 'tag': return 'علامة'
      default: return ''
    }
  }
  
  const recentSearches = getRecentSearches()
  const hasActiveFilters = Object.values(filters).some(value => {
    if (Array.isArray(value)) return value.length > 0
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== undefined && v !== null)
    }
    return value !== undefined && value !== null && value !== ''
  })
  
  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Skip link for keyboard navigation */}
      <a href="#search-results" className="skip-link">
        تخطي إلى النتائج
      </a>
      
      {/* Hidden help text for screen readers */}
      <div id="search-help" className="sr-only">
        استخدم الأسهم للتنقل بين الاقتراحات، اضغط Enter للبحث، أو Escape للإغلاق
      </div>
      
      {/* Search Mode Tabs - Responsive */}
      <Tabs value={searchMode} onValueChange={(value) => setSearchMode(value as 'simple' | 'advanced')}>
        <TabsList className={`grid w-full grid-cols-2 mb-4 ${isMobile ? 'h-auto' : ''}`} role="tablist" aria-label="أوضاع البحث">
          {SEARCH_MODES.map(mode => (
            <TabsTrigger 
              key={mode.id} 
              value={mode.id} 
              className={`flex flex-col gap-1 ${isMobile ? 'py-2 px-3' : 'py-3'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
              role="tab"
              aria-selected={searchMode === mode.id}
              aria-controls={`search-panel-${mode.id}`}
            >
              <span className={`font-medium ${isMobile ? 'text-sm' : ''}`}>{mode.label}</span>
              <span className={`text-xs text-gray-500 ${isMobile ? 'hidden' : ''}`}>{mode.description}</span>
            </TabsTrigger>
          ))}
        </TabsList>
        
        <TabsContent value="simple" className="space-y-4" role="tabpanel" id="search-panel-simple" aria-labelledby="search-tab-simple">
          {/* Simple Search */}
          <Card>
            <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
              <div className="relative">
                <div className={`flex gap-2 ${isMobile ? 'flex-col space-y-3' : ''}`}>
                  <div className="flex-1 relative">
                    <Search className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
                    <Input
                      ref={inputRef}
                      type="text"
                      placeholder={isMobile ? 'ابحث...' : placeholder}
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      onFocus={() => setShowSuggestions(true)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSearch()
                        }
                        if (e.key === 'Escape') {
                          setShowSuggestions(false)
                        }
                        if (e.key === 'ArrowDown' && showSuggestions && suggestions.length > 0) {
                          e.preventDefault()
                          // Focus first suggestion
                          const firstSuggestion = document.querySelector('[data-suggestion-index="0"]') as HTMLElement
                          firstSuggestion?.focus()
                        }
                      }}
                      className={`pr-10 pl-12 ${isMobile ? 'h-11 text-base' : 'h-12 text-lg'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                      dir={detectedLanguage === 'en' ? 'ltr' : 'rtl'}
                      autoComplete="off"
                      autoCapitalize="off"
                      autoCorrect="off"
                      spellCheck="false"
                      role="searchbox"
                      aria-label={placeholder}
                      aria-expanded={showSuggestions}
                      aria-haspopup="listbox"
                      aria-autocomplete="list"
                      aria-describedby="search-help"
                    />
                    
                    {/* Language indicator */}
                    <div className={`absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2 ${isMobile ? 'hidden' : ''}`}>
                      <Globe className="w-4 h-4 text-gray-400" />
                      <span className="text-xs text-gray-500 uppercase">
                        {detectedLanguage === 'auto' ? 'AR/EN' : detectedLanguage}
                      </span>
                    </div>
                    
                    {/* Clear button */}
                    {query && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setQuery('')
                          inputRef.current?.focus()
                        }}
                        className={`absolute ${isMobile ? 'left-3' : 'left-16'} top-1/2 transform -translate-y-1/2 p-1 h-auto ${isTouchDevice ? 'min-h-[44px] min-w-[44px]' : ''}`}
                        aria-label="مسح النص"
                        title="مسح النص"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  
                  <div className={`flex gap-2 ${isMobile ? 'justify-center' : ''}`}>
                    {/* Voice search button */}
                    {recognitionRef.current && (
                      <Button
                        variant="outline"
                        size={isMobile ? "default" : "lg"}
                        onClick={handleVoiceSearch}
                        className={`${isMobile ? 'px-3' : 'px-4'} ${isVoiceRecording ? 'bg-red-50 border-red-200 text-red-600' : ''} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                        disabled={isVoiceRecording}
                      >
                        {isVoiceRecording ? (
                          <MicOff className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} animate-pulse`} />
                        ) : (
                          <Mic className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
                        )}
                        {!isMobile && <span className="mr-2">صوت</span>}
                      </Button>
                    )}
                    
                    {/* Mobile filters button */}
                    {isMobile && showFilters && (
                      <Sheet open={isMobileFiltersOpen} onOpenChange={setIsMobileFiltersOpen}>
                        <SheetTrigger asChild>
                          <Button
                            variant="outline"
                            size="default"
                            className={`px-3 ${hasActiveFilters ? 'bg-blue-50 border-blue-200 text-blue-600' : ''} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                          >
                            <Filter className="w-4 h-4" />
                            {hasActiveFilters && (
                              <Badge variant="secondary" className="mr-1 text-xs">
                                {Object.values(filters).reduce((count, value) => {
                                  if (Array.isArray(value)) return count + value.length
                                  if (value && typeof value === 'object') {
                                    return count + Object.values(value).filter(v => v !== undefined && v !== null).length
                                  }
                                  return count + (value ? 1 : 0)
                                }, 0)}
                              </Badge>
                            )}
                          </Button>
                        </SheetTrigger>
                        <SheetContent side="right" className="w-full sm:max-w-md">
                          <SheetHeader>
                            <SheetTitle>فلاتر البحث</SheetTitle>
                          </SheetHeader>
                          <div className="mt-6">
                            <SearchFilters
                              filters={filters}
                              onFiltersChange={handleFiltersChange}
                              onClearFilters={() => handleFiltersChange({})}
                              className="border-0 shadow-none"
                            />
                          </div>
                        </SheetContent>
                      </Sheet>
                    )}
                    
                    {/* Search button */}
                    <Button 
                      onClick={() => handleSearch()} 
                      disabled={!query.trim()}
                      size={isMobile ? "default" : "lg"}
                      className={`${isMobile ? 'px-4 flex-1' : 'px-6'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                    >
                      بحث
                    </Button>
                  </div>
                </div>
                
                {/* Active filters indicator */}
                {hasActiveFilters && (
                  <div className="mt-3 flex items-center gap-2">
                    <Settings className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-600">فلاتر نشطة</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsAdvancedOpen(true)}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      عرض الفلاتر
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="advanced" className="space-y-4" role="tabpanel" id="search-panel-advanced" aria-labelledby="search-tab-advanced">
          {/* Advanced Search */}
          <Card>
            <CardHeader className={isMobile ? 'pb-3' : ''}>
              <CardTitle className={`flex items-center gap-2 ${isMobile ? 'text-lg' : ''}`}>
                <Sparkles className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
                البحث المتقدم
              </CardTitle>
            </CardHeader>
            <CardContent className={`space-y-6 ${isMobile ? 'p-4' : ''}`}>
              {/* Search input */}
              <div className="relative">
                <div className={`flex gap-2 ${isMobile ? 'flex-col space-y-3' : ''}`}>
                  <div className="flex-1 relative">
                    <Search className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
                    <Input
                      type="text"
                      placeholder={isMobile ? 'ابحث...' : placeholder}
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      onFocus={() => setShowSuggestions(true)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSearch()
                        }
                        if (e.key === 'Escape') {
                          setShowSuggestions(false)
                        }
                      }}
                      className={`pr-10 pl-12 ${isMobile ? 'h-11 text-base' : 'h-12 text-lg'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                      dir={detectedLanguage === 'en' ? 'ltr' : 'rtl'}
                      autoComplete="off"
                      autoCapitalize="off"
                      autoCorrect="off"
                      spellCheck="false"
                    />
                    
                    {/* Language indicator */}
                    <div className={`absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2 ${isMobile ? 'hidden' : ''}`}>
                      <Globe className="w-4 h-4 text-gray-400" />
                      <span className="text-xs text-gray-500 uppercase">
                        {detectedLanguage === 'auto' ? 'AR/EN' : detectedLanguage}
                      </span>
                    </div>
                    
                    {/* Clear button */}
                    {query && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setQuery('')}
                        className={`absolute ${isMobile ? 'left-3' : 'left-16'} top-1/2 transform -translate-y-1/2 p-1 h-auto ${isTouchDevice ? 'min-h-[44px] min-w-[44px]' : ''}`}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  
                  <div className={`flex gap-2 ${isMobile ? 'justify-center' : ''}`}>
                    {/* Voice search button */}
                    {recognitionRef.current && (
                      <Button
                        variant="outline"
                        size={isMobile ? "default" : "lg"}
                        onClick={handleVoiceSearch}
                        className={`${isMobile ? 'px-3' : 'px-4'} ${isVoiceRecording ? 'bg-red-50 border-red-200 text-red-600' : ''} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                        disabled={isVoiceRecording}
                      >
                        {isVoiceRecording ? (
                          <MicOff className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} animate-pulse`} />
                        ) : (
                          <Mic className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
                        )}
                        {!isMobile && <span className="mr-2">صوت</span>}
                      </Button>
                    )}
                    
                    {/* Search button */}
                    <Button 
                      onClick={() => handleSearch()} 
                      disabled={!query.trim()}
                      size={isMobile ? "default" : "lg"}
                      className={`${isMobile ? 'px-4 flex-1' : 'px-6'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                    >
                      بحث
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Filters */}
              {showFilters && !isMobile && (
                <SearchFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={() => handleFiltersChange({})}
                />
              )}
              
              {/* Mobile filters button for advanced mode */}
              {showFilters && isMobile && (
                <div className="flex justify-center">
                  <Sheet open={isMobileFiltersOpen} onOpenChange={setIsMobileFiltersOpen}>
                    <SheetTrigger asChild>
                      <Button
                        variant="outline"
                        className={`w-full ${hasActiveFilters ? 'bg-blue-50 border-blue-200 text-blue-600' : ''} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                      >
                        <Filter className="w-4 h-4 mr-2" />
                        فلاتر متقدمة
                        {hasActiveFilters && (
                          <Badge variant="secondary" className="mr-2">
                            {Object.values(filters).reduce((count, value) => {
                              if (Array.isArray(value)) return count + value.length
                              if (value && typeof value === 'object') {
                                return count + Object.values(value).filter(v => v !== undefined && v !== null).length
                              }
                              return count + (value ? 1 : 0)
                            }, 0)}
                          </Badge>
                        )}
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="right" className="w-full sm:max-w-md">
                      <SheetHeader>
                        <SheetTitle>فلاتر البحث المتقدم</SheetTitle>
                      </SheetHeader>
                      <div className="mt-6">
                        <SearchFilters
                          filters={filters}
                          onFiltersChange={handleFiltersChange}
                          onClearFilters={() => handleFiltersChange({})}
                          className="border-0 shadow-none"
                        />
                      </div>
                    </SheetContent>
                  </Sheet>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Suggestions Dropdown - Mobile Optimized */}
      {showSuggestions && (query.trim().length >= 2 || recentSearches.length > 0) && (
        <Card 
          className={`absolute top-full left-0 right-0 mt-2 ${isMobile ? 'max-h-80' : 'max-h-96'} overflow-y-auto z-50 shadow-lg ${isMobile ? 'mx-0' : ''}`}
          role="listbox"
          aria-label="اقتراحات البحث"
        >
          <CardContent className="p-0">
            {isLoadingSuggestions ? (
              <div className={`flex items-center justify-center ${isMobile ? 'p-4' : 'p-6'}`} role="status" aria-live="polite">
                <Loader2 className="w-5 h-5 animate-spin text-blue-600 mr-2" />
                <span className={`text-gray-600 ${isMobile ? 'text-sm' : ''}`}>
                  {isProgressiveLoading ? 'جاري التحميل...' : 'جاري تحميل الاقتراحات...'}
                </span>
              </div>
            ) : (
              <div className="space-y-1">
                {/* Search suggestions */}
                {suggestions.length > 0 && (
                  <>
                    <div className={`${isMobile ? 'p-2' : 'p-3'} border-b`} role="group" aria-labelledby="suggestions-heading">
                      <h3 id="suggestions-heading" className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-900`}>
                        اقتراحات البحث
                      </h3>
                    </div>
                    
                    {suggestions.slice(0, isMobile ? 4 : 8).map((suggestion, index) => (
                      <div
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault()
                            handleSuggestionClick(suggestion)
                          }
                          if (e.key === 'ArrowDown') {
                            e.preventDefault()
                            const nextElement = document.querySelector(`[data-suggestion-index="${index + 1}"]`) as HTMLElement
                            nextElement?.focus()
                          }
                          if (e.key === 'ArrowUp') {
                            e.preventDefault()
                            if (index === 0) {
                              inputRef.current?.focus()
                            } else {
                              const prevElement = document.querySelector(`[data-suggestion-index="${index - 1}"]`) as HTMLElement
                              prevElement?.focus()
                            }
                          }
                          if (e.key === 'Escape') {
                            setShowSuggestions(false)
                            inputRef.current?.focus()
                          }
                        }}
                        className={`flex items-center gap-3 ${isMobile ? 'p-2' : 'p-3'} hover:bg-gray-50 active:bg-gray-100 cursor-pointer transition-colors ${isTouchDevice ? 'min-h-[44px]' : ''} focus:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset`}
                        role="option"
                        tabIndex={0}
                        data-suggestion-index={index}
                        aria-label={`اقتراح: ${suggestion.text}${suggestion.type !== 'recent' ? ` (${getSuggestionTypeLabel(suggestion.type)})` : ''}`}
                      >
                        {getSuggestionIcon(suggestion.type)}
                        <div className="flex-1 min-w-0">
                          <span className={`text-gray-700 ${isMobile ? 'text-sm' : ''} truncate block`}>
                            {suggestion.text}
                          </span>
                          {suggestion.type !== 'recent' && !isMobile && (
                            <span className="text-xs text-gray-500 mr-2">
                              ({getSuggestionTypeLabel(suggestion.type)})
                            </span>
                          )}
                        </div>
                        {suggestion.count && !isMobile && (
                          <Badge variant="outline" className="text-xs">
                            {suggestion.count}
                          </Badge>
                        )}
                        <ArrowRight className="w-4 h-4 text-gray-400 flex-shrink-0" />
                      </div>
                    ))}
                    
                    {/* Show more suggestions on mobile */}
                    {isMobile && suggestions.length > 4 && (
                      <div className="p-2 border-t">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-full text-xs"
                          onClick={() => {
                            // Show all suggestions by updating the slice limit
                            // This is a simple implementation - in production you might want pagination
                          }}
                        >
                          عرض {suggestions.length - 4} اقتراح إضافي
                        </Button>
                      </div>
                    )}
                  </>
                )}
                
                {/* Recent searches */}
                {recentSearches.length > 0 && suggestions.length === 0 && (
                  <>
                    <div className={`${isMobile ? 'p-2' : 'p-3'} border-b`}>
                      <h3 className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-900`}>
                        عمليات البحث الأخيرة
                      </h3>
                    </div>
                    
                    {recentSearches.slice(0, isMobile ? 3 : 5).map((search, index) => (
                      <div
                        key={index}
                        onClick={() => handleSuggestionClick({ text: search, type: 'recent' })}
                        className={`flex items-center gap-3 ${isMobile ? 'p-2' : 'p-3'} hover:bg-gray-50 active:bg-gray-100 cursor-pointer transition-colors ${isTouchDevice ? 'min-h-[44px]' : ''}`}
                      >
                        <History className="w-4 h-4 text-gray-400" />
                        <span className={`flex-1 text-gray-700 ${isMobile ? 'text-sm' : ''} truncate`}>
                          {search}
                        </span>
                        <ArrowRight className="w-4 h-4 text-gray-400" />
                      </div>
                    ))}
                  </>
                )}
                
                {/* No suggestions */}
                {suggestions.length === 0 && recentSearches.length === 0 && query.trim().length >= 2 && (
                  <div className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
                    <Search className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} text-gray-400 mx-auto mb-2`} />
                    <p className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      لا توجد اقتراحات لـ "{query.length > 20 && isMobile ? query.substring(0, 20) + '...' : query}"
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
      
      {/* Collapsible filters for simple mode - Desktop only */}
      {searchMode === 'simple' && showFilters && !isMobile && (
        <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full mt-4 flex items-center justify-center gap-2">
              <Settings className="w-4 h-4" />
              فلاتر متقدمة
              {isAdvancedOpen ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-4">
            <SearchFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onClearFilters={() => handleFiltersChange({})}
            />
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  )
}

// Extend Window interface for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any
    webkitSpeechRecognition: any
  }
}